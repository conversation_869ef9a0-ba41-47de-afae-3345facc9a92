import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Info, XCircle, X } from 'lucide-react';
import { cn } from "@/lib/utils";

const AlertBox = ({
  title = "Notification",
  content,
  type = 'default',
  onClose,
  duration = 3000
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!duration) return;

    const timer = setTimeout(() => {
      setVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!visible || !content) return null;

  const variants = {
    success: {
      icon: CheckCircle,
      iconColor: "text-green-600 dark:text-green-400"
    },
    danger: {
      icon: XCircle,
      iconColor: "text-red-600 dark:text-red-400"
    },
    info: {
      icon: Info,
      iconColor: "text-primary-600 dark:text-primary-400"
    },
    warning: {
      icon: AlertCircle,
      iconColor: "text-yellow-600 dark:text-yellow-400"
    },
    error: {
      icon: XCircle,
      iconColor: "text-red-600 dark:text-red-400"
    },
    default: {
      icon: Info,
      iconColor: "text-primary-600 dark:text-primary-400"
    }
  };

  const variant = variants[type] || variants.default;
  const Icon = variant.icon;

  return (
    <div
      role="alert"
      className={cn(
        "toaster",
        `toaster-${type}`
      )}
    >
      <div className="toaster-content">
        <div className="toaster-icon">
          <Icon className="w-full h-full" />
        </div>

        <div className="toaster-body">
          <div className="toaster-title">
            {title}
          </div>
          {content && (
            <div className="toaster-message">
              {content}
            </div>
          )}
        </div>

        <button
          onClick={onClose}
          className="toaster-close"
          aria-label="Close notification"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default AlertBox;
