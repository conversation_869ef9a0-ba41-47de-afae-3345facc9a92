@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Toaster Container */
  .toaster-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toaster-container > * {
    pointer-events: auto;
  }

  /* Base Toaster Styles */
  .toaster {
    min-width: 22rem;
    max-width: 26rem;
    padding: 1.25rem;
    border-radius: 0.75rem;
    border: 2px solid;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-gray-900) / 0.2),
                0 4px 12px -2px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.05) inset;
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateX(0);
    opacity: 1;
  }

  /* Toaster Animation States */
  .toaster-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster-exit {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 35px -8px hsl(var(--semantic-gray-900) / 0.25),
                0 8px 20px -4px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.08) inset;
  }

  /* Toaster Content Layout */
  .toaster-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .toaster-icon {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
  }

  .toaster-body {
    flex: 1;
    min-width: 0;
  }

  .toaster-title {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-base);
    line-height: 1.4;
    margin-bottom: 0.375rem;
  }

  .toaster-message {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    opacity: 0.95;
  }

  .toaster-close {
    flex-shrink: 0;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.25rem;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    margin-top: -0.125rem;
    margin-right: -0.25rem;
  }

  .toaster-close:hover {
    background-color: hsl(var(--semantic-gray-100));
  }

  .toaster-close:focus {
    outline: none;
    ring: 2px;
    ring-color: hsl(var(--primary) / 0.5);
    ring-offset: 1px;
  }

  /* Common Toaster Variants - Works in both light and dark themes */
  .toaster-success {
    background: #22c55e !important;
    border: 2px solid #16a34a !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(34, 197, 94, 0.5) !important;
  }

  .toaster-error,
  .toaster-danger {
    background: #ef4444 !important;
    border: 2px solid #dc2626 !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(239, 68, 68, 0.5) !important;
  }

  .toaster-warning {
    background: #f59e0b !important;
    border: 2px solid #d97706 !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(245, 158, 11, 0.5) !important;
  }

  .toaster-info,
  .toaster-default {
    background: #f97316 !important;
    border: 2px solid #ea580c !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(249, 115, 22, 0.5) !important;
  }

  /* Common styling for all variants */
  .toaster-success .toaster-icon,
  .toaster-error .toaster-icon,
  .toaster-danger .toaster-icon,
  .toaster-warning .toaster-icon,
  .toaster-info .toaster-icon,
  .toaster-default .toaster-icon {
    color: white !important;
  }

  .toaster-success .toaster-title,
  .toaster-error .toaster-title,
  .toaster-danger .toaster-title,
  .toaster-warning .toaster-title,
  .toaster-info .toaster-title,
  .toaster-default .toaster-title {
    color: white !important;
    font-weight: bold !important;
  }

  .toaster-success .toaster-message,
  .toaster-error .toaster-message,
  .toaster-danger .toaster-message,
  .toaster-warning .toaster-message,
  .toaster-info .toaster-message,
  .toaster-default .toaster-message {
    color: white !important;
    opacity: 0.95 !important;
  }

  .toaster-success .toaster-close:hover,
  .toaster-error .toaster-close:hover,
  .toaster-danger .toaster-close:hover,
  .toaster-warning .toaster-close:hover,
  .toaster-info .toaster-close:hover,
  .toaster-default .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }





  /* Responsive Design */
  @media (max-width: 640px) {
    .toaster-container {
      top: 1rem;
      right: 1rem;
      left: 1rem;
    }

    .toaster {
      min-width: auto;
      max-width: none;
      width: 100%;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .toaster {
      transition: none;
    }

    .toaster:hover {
      transform: none;
    }
  }
}
