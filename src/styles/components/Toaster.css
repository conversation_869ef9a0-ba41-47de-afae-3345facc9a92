@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Toaster Container */
  .toaster-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toaster-container > * {
    pointer-events: auto;
  }

  /* Base Toaster Styles */
  .toaster {
    min-width: 22rem;
    max-width: 26rem;
    padding: 1.25rem;
    border-radius: 0.75rem;
    border: 2px solid;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-gray-900) / 0.2),
                0 4px 12px -2px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.05) inset;
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateX(0);
    opacity: 1;
  }

  /* Toaster Animation States */
  .toaster-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster-exit {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 35px -8px hsl(var(--semantic-gray-900) / 0.25),
                0 8px 20px -4px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.08) inset;
  }

  /* Toaster Content Layout */
  .toaster-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .toaster-icon {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
  }

  .toaster-body {
    flex: 1;
    min-width: 0;
  }

  .toaster-title {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-base);
    line-height: 1.4;
    margin-bottom: 0.375rem;
  }

  .toaster-message {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    opacity: 0.95;
  }

  .toaster-close {
    flex-shrink: 0;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.25rem;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    margin-top: -0.125rem;
    margin-right: -0.25rem;
  }

  .toaster-close:hover {
    background-color: hsl(var(--semantic-gray-100));
  }

  .toaster-close:focus {
    outline: none;
    ring: 2px;
    ring-color: hsl(var(--primary) / 0.5);
    ring-offset: 1px;
  }

  /* Success Variant */
  .toaster-success {
    background: linear-gradient(135deg,
      hsl(var(--semantic-green-50)) 0%,
      hsl(var(--semantic-green-100)) 100%);
    border-color: hsl(var(--semantic-green-400));
    color: hsl(var(--semantic-green-900));
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-green-500) / 0.3),
                0 4px 12px -2px hsl(var(--semantic-green-500) / 0.2),
                0 0 0 1px hsl(var(--semantic-green-300) / 0.5) inset;
  }

  .toaster-success .toaster-icon {
    color: hsl(var(--semantic-green-700));
  }

  .toaster-success .toaster-close:hover {
    background-color: hsl(var(--semantic-green-200));
  }

  /* Error Variant */
  .toaster-error {
    background: linear-gradient(135deg,
      hsl(var(--semantic-red-50)) 0%,
      hsl(var(--semantic-red-100)) 100%);
    border-color: hsl(var(--semantic-red-400));
    color: hsl(var(--semantic-red-900));
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-red-500) / 0.3),
                0 4px 12px -2px hsl(var(--semantic-red-500) / 0.2),
                0 0 0 1px hsl(var(--semantic-red-300) / 0.5) inset;
  }

  .toaster-error .toaster-icon {
    color: hsl(var(--semantic-red-700));
  }

  .toaster-error .toaster-close:hover {
    background-color: hsl(var(--semantic-red-200));
  }

  /* Warning Variant */
  .toaster-warning {
    background: linear-gradient(135deg,
      hsl(var(--semantic-yellow-50)) 0%,
      hsl(var(--semantic-yellow-100)) 100%);
    border-color: hsl(var(--semantic-yellow-400));
    color: hsl(var(--semantic-yellow-900));
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-yellow-500) / 0.3),
                0 4px 12px -2px hsl(var(--semantic-yellow-500) / 0.2),
                0 0 0 1px hsl(var(--semantic-yellow-300) / 0.5) inset;
  }

  .toaster-warning .toaster-icon {
    color: hsl(var(--semantic-yellow-700));
  }

  .toaster-warning .toaster-close:hover {
    background-color: hsl(var(--semantic-yellow-200));
  }

  /* Info Variant */
  .toaster-info {
    background: linear-gradient(135deg,
      hsl(var(--primary-50)) 0%,
      hsl(var(--primary-100)) 100%);
    border-color: hsl(var(--primary-400));
    color: hsl(var(--primary-900));
    box-shadow: 0 8px 25px -5px hsl(var(--primary) / 0.3),
                0 4px 12px -2px hsl(var(--primary) / 0.2),
                0 0 0 1px hsl(var(--primary-300) / 0.5) inset;
  }

  .toaster-info .toaster-icon {
    color: hsl(var(--primary-700));
  }

  .toaster-info .toaster-close:hover {
    background-color: hsl(var(--primary-200));
  }

  /* Dark Theme Adjustments */
  .dark .toaster {
    backdrop-filter: blur(16px) saturate(140%);
    -webkit-backdrop-filter: blur(16px) saturate(140%);
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-gray-900) / 0.5),
                0 4px 12px -2px hsl(var(--semantic-gray-900) / 0.4),
                0 0 0 1px hsl(var(--semantic-gray-700) / 0.3) inset;
  }

  .dark .toaster:hover {
    box-shadow: 0 12px 35px -8px hsl(var(--semantic-gray-900) / 0.6),
                0 8px 20px -4px hsl(var(--semantic-gray-900) / 0.5),
                0 0 0 1px hsl(var(--semantic-gray-600) / 0.4) inset;
  }

  .dark .toaster-close:hover {
    background-color: hsl(var(--semantic-gray-800));
  }

  /* Dark Theme Success */
  .dark .toaster-success {
    background: linear-gradient(135deg,
      hsl(var(--semantic-green-900)) 0%,
      hsl(var(--semantic-green-800)) 100%);
    border-color: hsl(var(--semantic-green-600));
    color: hsl(var(--semantic-green-100));
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-green-600) / 0.4),
                0 4px 12px -2px hsl(var(--semantic-green-600) / 0.3),
                0 0 0 1px hsl(var(--semantic-green-700) / 0.5) inset;
  }

  .dark .toaster-success .toaster-icon {
    color: hsl(var(--semantic-green-400));
  }

  .dark .toaster-success .toaster-close:hover {
    background-color: hsl(var(--semantic-green-700));
  }

  /* Dark Theme Error */
  .dark .toaster-error {
    background: linear-gradient(135deg,
      hsl(var(--semantic-red-900)) 0%,
      hsl(var(--semantic-red-800)) 100%);
    border-color: hsl(var(--semantic-red-600));
    color: hsl(var(--semantic-red-100));
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-red-600) / 0.4),
                0 4px 12px -2px hsl(var(--semantic-red-600) / 0.3),
                0 0 0 1px hsl(var(--semantic-red-700) / 0.5) inset;
  }

  .dark .toaster-error .toaster-icon {
    color: hsl(var(--semantic-red-400));
  }

  .dark .toaster-error .toaster-close:hover {
    background-color: hsl(var(--semantic-red-700));
  }

  /* Dark Theme Warning */
  .dark .toaster-warning {
    background: linear-gradient(135deg,
      hsl(var(--semantic-yellow-900)) 0%,
      hsl(var(--semantic-yellow-800)) 100%);
    border-color: hsl(var(--semantic-yellow-600));
    color: hsl(var(--semantic-yellow-100));
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-yellow-600) / 0.4),
                0 4px 12px -2px hsl(var(--semantic-yellow-600) / 0.3),
                0 0 0 1px hsl(var(--semantic-yellow-700) / 0.5) inset;
  }

  .dark .toaster-warning .toaster-icon {
    color: hsl(var(--semantic-yellow-400));
  }

  .dark .toaster-warning .toaster-close:hover {
    background-color: hsl(var(--semantic-yellow-700));
  }

  /* Dark Theme Info */
  .dark .toaster-info {
    background: linear-gradient(135deg,
      hsl(var(--primary-900)) 0%,
      hsl(var(--primary-800)) 100%);
    border-color: hsl(var(--primary-600));
    color: hsl(var(--primary-100));
    box-shadow: 0 8px 25px -5px hsl(var(--primary) / 0.4),
                0 4px 12px -2px hsl(var(--primary) / 0.3),
                0 0 0 1px hsl(var(--primary-700) / 0.5) inset;
  }

  .dark .toaster-info .toaster-icon {
    color: hsl(var(--primary-400));
  }

  .dark .toaster-info .toaster-close:hover {
    background-color: hsl(var(--primary-700));
  }

  /* Responsive Design */
  @media (max-width: 640px) {
    .toaster-container {
      top: 1rem;
      right: 1rem;
      left: 1rem;
    }

    .toaster {
      min-width: auto;
      max-width: none;
      width: 100%;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .toaster {
      transition: none;
    }

    .toaster:hover {
      transform: none;
    }
  }
}
