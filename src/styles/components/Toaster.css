@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Toaster Container */
  .toaster-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toaster-container > * {
    pointer-events: auto;
  }

  /* Base Toaster Styles */
  .toaster {
    min-width: 22rem;
    max-width: 26rem;
    padding: 1.25rem;
    border-radius: 0.75rem;
    border: 2px solid;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-gray-900) / 0.2),
                0 4px 12px -2px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.05) inset;
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateX(0);
    opacity: 1;
  }

  /* Toaster Animation States */
  .toaster-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster-exit {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 35px -8px hsl(var(--semantic-gray-900) / 0.25),
                0 8px 20px -4px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.08) inset;
  }

  /* Toaster Content Layout */
  .toaster-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .toaster-icon {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
  }

  .toaster-body {
    flex: 1;
    min-width: 0;
  }

  .toaster-title {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-base);
    line-height: 1.4;
    margin-bottom: 0.375rem;
  }

  .toaster-message {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    opacity: 0.95;
  }

  .toaster-close {
    flex-shrink: 0;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.25rem;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    margin-top: -0.125rem;
    margin-right: -0.25rem;
  }

  .toaster-close:hover {
    background-color: hsl(var(--semantic-gray-100));
  }

  .toaster-close:focus {
    outline: none;
    ring: 2px;
    ring-color: hsl(var(--primary) / 0.5);
    ring-offset: 1px;
  }

  /* Success Variant */
  .toaster-success {
    background: linear-gradient(135deg,
      hsl(var(--semantic-green-600)) 0%,
      hsl(var(--semantic-green-700)) 100%);
    border-color: hsl(var(--semantic-green-500));
    color: white;
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-green-600) / 0.4),
                0 4px 12px -2px hsl(var(--semantic-green-600) / 0.3),
                0 0 0 1px hsl(var(--semantic-green-500) / 0.3) inset;
  }

  .toaster-success .toaster-icon {
    color: white;
  }

  .toaster-success .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  /* Error Variant */
  .toaster-error {
    background: linear-gradient(135deg,
      hsl(var(--semantic-red-600)) 0%,
      hsl(var(--semantic-red-700)) 100%);
    border-color: hsl(var(--semantic-red-500));
    color: white;
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-red-600) / 0.4),
                0 4px 12px -2px hsl(var(--semantic-red-600) / 0.3),
                0 0 0 1px hsl(var(--semantic-red-500) / 0.3) inset;
  }

  .toaster-error .toaster-icon {
    color: white;
  }

  .toaster-error .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  /* Warning Variant */
  .toaster-warning {
    background: linear-gradient(135deg,
      hsl(var(--semantic-yellow-600)) 0%,
      hsl(var(--semantic-yellow-700)) 100%);
    border-color: hsl(var(--semantic-yellow-500));
    color: white;
    box-shadow: 0 8px 25px -5px hsl(var(--semantic-yellow-600) / 0.4),
                0 4px 12px -2px hsl(var(--semantic-yellow-600) / 0.3),
                0 0 0 1px hsl(var(--semantic-yellow-500) / 0.3) inset;
  }

  .toaster-warning .toaster-icon {
    color: white;
  }

  .toaster-warning .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  /* Info Variant */
  .toaster-info {
    background: linear-gradient(135deg,
      hsl(var(--primary-600)) 0%,
      hsl(var(--primary-700)) 100%);
    border-color: hsl(var(--primary-500));
    color: white;
    box-shadow: 0 8px 25px -5px hsl(var(--primary) / 0.4),
                0 4px 12px -2px hsl(var(--primary) / 0.3),
                0 0 0 1px hsl(var(--primary-500) / 0.3) inset;
  }

  .toaster-info .toaster-icon {
    color: white;
  }

  .toaster-info .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }





  /* Responsive Design */
  @media (max-width: 640px) {
    .toaster-container {
      top: 1rem;
      right: 1rem;
      left: 1rem;
    }

    .toaster {
      min-width: auto;
      max-width: none;
      width: 100%;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .toaster {
      transition: none;
    }

    .toaster:hover {
      transform: none;
    }
  }
}
