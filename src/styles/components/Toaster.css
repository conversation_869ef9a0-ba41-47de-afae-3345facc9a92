@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Toaster Container */
  .toaster-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toaster-container > * {
    pointer-events: auto;
  }

  /* Base Toaster Styles */
  .toaster {
    min-width: 22rem !important;
    max-width: 26rem !important;
    padding: 1.25rem !important;
    border-radius: 0.75rem !important;
    font-family: 'Inter', sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
    transform: translateX(0) !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10000 !important;
  }

  /* Toaster Animation States */
  .toaster-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster-exit {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 35px -8px hsl(var(--semantic-gray-900) / 0.25),
                0 8px 20px -4px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.08) inset;
  }

  /* Toaster Content Layout */
  .toaster-content {
    display: flex !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
  }

  .toaster-icon {
    flex-shrink: 0 !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    margin-top: 0.125rem !important;
  }

  .toaster-body {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .toaster-title {
    font-weight: bold !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    margin-bottom: 0.375rem !important;
  }

  .toaster-message {
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    opacity: 0.95 !important;
  }

  .toaster-close {
    flex-shrink: 0 !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
    border-radius: 0.25rem !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease-in-out !important;
    margin-top: -0.125rem !important;
    margin-right: -0.25rem !important;
    color: white !important;
  }

  .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .toaster-close:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5) !important;
  }

  /* Common Toaster Variants - Works in both light and dark themes */
  div.toaster.toaster-success {
    background: #22c55e !important;
    border: 2px solid #16a34a !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(34, 197, 94, 0.5) !important;
  }

  div.toaster.toaster-error,
  div.toaster.toaster-danger {
    background: #ef4444 !important;
    border: 2px solid #dc2626 !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(239, 68, 68, 0.5) !important;
  }

  div.toaster.toaster-warning {
    background: #f59e0b !important;
    border: 2px solid #d97706 !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(245, 158, 11, 0.5) !important;
  }

  div.toaster.toaster-info,
  div.toaster.toaster-default {
    background: #f97316 !important;
    border: 2px solid #ea580c !important;
    color: white !important;
    box-shadow: 0 10px 30px -5px rgba(249, 115, 22, 0.5) !important;
  }

  /* Common styling for all variants */
  div.toaster .toaster-icon {
    color: white !important;
  }

  div.toaster .toaster-title {
    color: white !important;
    font-weight: bold !important;
  }

  div.toaster .toaster-message {
    color: white !important;
    opacity: 0.95 !important;
  }

  div.toaster .toaster-close {
    color: white !important;
  }

  div.toaster .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }





  /* Responsive Design */
  @media (max-width: 640px) {
    .toaster-container {
      top: 1rem;
      right: 1rem;
      left: 1rem;
    }

    .toaster {
      min-width: auto;
      max-width: none;
      width: 100%;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .toaster {
      transition: none;
    }

    .toaster:hover {
      transform: none;
    }
  }
}
